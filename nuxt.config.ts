// https://nuxt.com/docs/api/configuration/nuxt-config
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import path from 'path'

// 获取环境变量或使用默认值
const apiBase = process.env.NUXT_PUBLIC_API_BASE || 'https://api.dinq.io'

export default defineNuxtConfig({
  compatibilityDate: '2024-11-01',
  devtools: { enabled: true },
  modules: ['@nuxt/image', '@unocss/nuxt', 'motion-v/nuxt'],
  builder: 'vite',
    plugins: ['~/plugins/globals.js'],
    
  // 全局 CSS
  css: [
    '~/assets/css/fonts.css',
    '~/assets/styles/global.css'
  ],
    
  // 运行时配置，可以在应用中通过useRuntimeConfig()访问
  runtimeConfig: {
    // 公共变量，在客户端和服务器端都可用
    public: {
      apiBase,
      // 预渲染模式标志，用于在预渲染期间模拟 API 响应
      // 在混合渲染模式下，只有真正的预渲染阶段才需要模拟
      isPrerenderMode: false, // 禁用预渲染模拟，因为我们现在使用混合渲染
    }
  },

  // 恢复混合渲染模式 - 这是为了 SEO 和社交分享的核心需求
  nitro: {
    preset: 'cloudflare-pages',
    output: {
      dir: '.output',
      publicDir: '.output/public'
    },
    publicAssets: [
      {
        baseURL: '/',
        dir: 'public'
      }
    ],
    prerender: {
      routes: ['/'],
      crawlLinks: false,
      failOnError: false
    }
  },

  routeRules: {
    // 首页预渲染
    '/': { prerender: true },

    // GitHub 和 Compare 页面使用 SSR - 这是关键！
    '/github/**': { prerender: false },
    '/github_compare': { prerender: false }, // GitHub比较页面的新路径
    '/compare': { prerender: false },    // 修复：Scholar比较页面的正确路径
    '/compare/**': { prerender: false }, // 保留：以防有子路径

    // Scholar 分析页面使用 SSR - 新增！
    '/report/**': { prerender: false },

    // 调试页面不预渲染
    '/debug-meta': { prerender: false },

    // API 路由
    '/api/**': { prerender: false }
  },

  vite:{
    // 将环境变量暴露给客户端
    define: {
      'import.meta.env.NUXT_PUBLIC_API_BASE': JSON.stringify(apiBase)
    },
    plugins: [
      createSvgIconsPlugin({
        iconDirs: [path.resolve(process.cwd(), 'assets/svg')],
        symbolId: 'icon-[dir]-[name]',
      }),
    ],
    server:{
      cors: true,
      proxy: {
        '/api/v1': {
          target: 'https://search.dinq.io',
          changeOrigin: true}
      }
    }
  }
})